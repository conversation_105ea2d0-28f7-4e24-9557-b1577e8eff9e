import { useState } from "react";
import { Trash2, X } from "lucide-react";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { useContacts } from "@/hooks/useContacts";

interface SavedFacesDrawerProps {
  children: React.ReactNode;
}

const SavedFacesDrawer = ({ children }: SavedFacesDrawerProps) => {
  const { contacts, deleteContact } = useContacts();
  const [isOpen, setIsOpen] = useState(false);

  const handleDeleteContact = (id: string, name: string) => {
    if (window.confirm(`Are you sure you want to delete ${name}?`)) {
      deleteContact(id);
    }
  };

  return (
    <Drawer open={isOpen} onOpenChange={setIsOpen}>
      <DrawerTrigger asChild>
        {children}
      </DrawerTrigger>
      <DrawerContent className="max-h-[80vh]">
        <DrawerHeader className="text-center">
          <DrawerTitle className="text-xl font-bold text-gray-800">
            Saved Faces ({contacts.length})
          </DrawerTitle>
          <DrawerDescription className="text-gray-600">
            All your tagged contacts and faces
          </DrawerDescription>
        </DrawerHeader>
        
        <div className="px-4 pb-6 overflow-y-auto">
          {contacts.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">👤</div>
              <h3 className="text-lg font-semibold text-gray-700 mb-2">No Saved Faces</h3>
              <p className="text-gray-500">
                Start tagging faces in photos to build your contact database!
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {contacts.map((contact) => (
                <div
                  key={contact.id}
                  className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 flex items-center space-x-4 hover:shadow-md transition-shadow"
                >
                  <img
                    src={contact.imageUrl}
                    alt={contact.name}
                    className="w-16 h-16 rounded-full object-cover border-2 border-gray-100"
                  />
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-gray-800 truncate">
                      {contact.name}
                    </h3>
                    <p className="text-sm text-gray-500">
                      Tagged on {new Date(contact.timestamp).toLocaleDateString()}
                    </p>
                    <p className="text-xs text-gray-400">
                      {new Date(contact.timestamp).toLocaleTimeString()}
                    </p>
                  </div>
                  <button
                    onClick={() => handleDeleteContact(contact.id, contact.name)}
                    className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors flex-shrink-0"
                    title={`Delete ${contact.name}`}
                  >
                    <Trash2 className="w-5 h-5" />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
        
        <div className="px-4 pb-4 border-t border-gray-100 pt-4">
          <DrawerClose asChild>
            <button className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors flex items-center justify-center space-x-2">
              <X className="w-4 h-4" />
              <span>Close</span>
            </button>
          </DrawerClose>
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default SavedFacesDrawer;
