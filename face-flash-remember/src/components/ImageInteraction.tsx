import { useState, useRef } from "react";
import { Plus, Upload, Loader2 } from "lucide-react";
import { detectFaces, createImageFromFile, DetectedFace } from "@/utils/faceDetection";

interface ImageInteractionProps {
  onSaveContact: (name: string, imageUrl: string) => void;
  currentImage: string;
  onImageChange: (imageUrl: string) => void;
}

const ImageInteraction = ({ onSaveContact, currentImage, onImageChange }: ImageInteractionProps) => {
  const [selectedFace, setSelectedFace] = useState<number | null>(null);
  const [nameInput, setNameInput] = useState("");
  const [faces, setFaces] = useState<DetectedFace[]>([]);
  const [isDetecting, setIsDetecting] = useState(false);
  const [detectionError, setDetectionError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  const handleFaceClick = (faceId: number) => {
    setSelectedFace(faceId);
  };

  const handleSaveName = () => {
    if (nameInput.trim()) {
      onSaveContact(nameInput, currentImage);
      setNameInput("");
      setSelectedFace(null);
    }
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const imageUrl = URL.createObjectURL(file);
      onImageChange(imageUrl);
      setDetectionError(null);
      // Auto-detect faces in the uploaded image
      setIsDetecting(true);
      const img = await createImageFromFile(file);
      console.log('Image loaded, dimensions:', img.width, 'x', img.height);
      const detectedFaces = await detectFaces(img);
      console.log('Detected faces:', detectedFaces);
      setFaces(detectedFaces);
      if (detectedFaces.length === 0) {
        setDetectionError('No faces detected. Try adjusting the image or lighting.');
      }
      setIsDetecting(false);
    } catch (error) {
      console.error('Error in handleImageUpload:', error);
      setDetectionError('Error uploading or detecting faces. Please try again.');
      setIsDetecting(false);
    }
  };

  const handleDetectFaces = async () => {
    setIsDetecting(true);
    setDetectionError(null);
    try {
      const imgEl = imageRef.current;
      if (!imgEl) throw new Error('Image not loaded');

      // Ensure image is loaded
      if (!imgEl.complete) {
        await new Promise((resolve) => {
          imgEl.onload = resolve;
        });
      }

      console.log('Detecting faces in current image, dimensions:', imgEl.width, 'x', imgEl.height);
      const detectedFaces = await detectFaces(imgEl);
      console.log('Detected faces:', detectedFaces);
      setFaces(detectedFaces);
      if (detectedFaces.length === 0) {
        setDetectionError('No faces detected. Try adjusting the image or lighting.');
      }
      setIsDetecting(false);
    } catch (error) {
      console.error('Error in handleDetectFaces:', error);
      setDetectionError('Error detecting faces. Please try again.');
      setIsDetecting(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="relative bg-white rounded-2xl shadow-lg overflow-hidden">
        <div className="relative">
          <img
            ref={imageRef}
            src={currentImage}
            alt="Photo for face tagging"
            className="w-full h-64 object-cover"
            crossOrigin="anonymous"
          />
          {/* Loading overlay for face detection */}
          {isDetecting && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <div className="text-white text-center">
                <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
                <p>Detecting faces...</p>
              </div>
            </div>
          )}
          {/* Face bounding boxes */}
          {!isDetecting && faces.map((face) => (
            <div
              key={face.id}
              className={`absolute border-2 rounded-lg cursor-pointer transition-all duration-200 hover:scale-105 ${
                selectedFace === face.id
                  ? "border-blue-500 bg-blue-500 bg-opacity-20"
                  : "border-white bg-white bg-opacity-10"
              }`}
              style={{
                left: `${face.x}%`,
                top: `${face.y}%`,
                width: `${face.width}%`,
                height: `${face.height}%`,
              }}
              onClick={() => handleFaceClick(face.id)}
            >
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                <Plus className="w-3 h-3 text-white" />
              </div>
              {selectedFace !== face.id && (
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white text-xs p-1 text-center rounded-b-lg">
                  Tap to tag
                </div>
              )}
            </div>
          ))}
        </div>
        {/* Upload button overlay */}
        <div className="absolute top-4 right-4">
          <button
            onClick={() => fileInputRef.current?.click()}
            className="bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 shadow-lg transition-all"
          >
            <Upload className="w-5 h-5 text-gray-700" />
          </button>
        </div>
      </div>
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        className="hidden"
      />
      {/* Detect Faces Button */}
      <button
        onClick={handleDetectFaces}
        disabled={isDetecting}
        className="w-full bg-purple-500 text-white py-2 px-4 rounded-lg font-medium hover:bg-purple-600 transition-colors disabled:opacity-50 flex items-center justify-center space-x-2"
      >
        {isDetecting ? (
          <>
            <Loader2 className="w-4 h-4 animate-spin" />
            <span>Detecting...</span>
          </>
        ) : (
          <span>🔍 Detect Faces</span>
        )}
      </button>
      {/* Detection error or no faces feedback */}
      {detectionError && (
        <div className="text-center text-red-500 text-sm font-medium animate-fade-in">
          {detectionError}
        </div>
      )}
      {/* Name input modal */}
      {selectedFace && (
        <div className="bg-white rounded-xl shadow-lg p-4 border border-gray-100 animate-fade-in">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">Tag this person</h3>
          <div className="space-y-3">
            <input
              type="text"
              placeholder="Enter name..."
              value={nameInput}
              onChange={(e) => setNameInput(e.target.value)}
              className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
              autoFocus
            />
            <div className="flex space-x-2">
              <button
                onClick={handleSaveName}
                className="flex-1 bg-blue-500 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-600 transition-colors"
              >
                Save
              </button>
              <button
                onClick={() => setSelectedFace(null)}
                className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageInteraction;
