import { Camera, Upload, User } from "lucide-react";
import { useRef } from "react";
import SavedFacesDrawer from "./SavedFacesDrawer";

interface ActionButtonsProps {
  onImageUpload?: (file: File) => void;
}

const ActionButtons = ({ onImageUpload }: ActionButtonsProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && onImageUpload) {
      onImageUpload(file);
    }
  };

  const handleCameraClick = () => {
    cameraInputRef.current?.click();
  };

  const buttons = [
    {
      label: "Take Photo",
      icon: Camera,
      variant: "primary" as const,
      description: "Capture a new photo",
      onClick: handleCameraClick
    },
    {
      label: "Upload Photo",
      icon: Upload,
      variant: "secondary" as const,
      description: "Choose from gallery",
      onClick: handleUploadClick
    },
    {
      label: "Review Saved Faces",
      icon: User,
      variant: "secondary" as const,
      description: "View tagged contacts",
      isDrawerTrigger: true
    },
  ];

  return (
    <div className="space-y-3">
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
      />
      <input
        ref={cameraInputRef}
        type="file"
        accept="image/*"
        capture="environment"
        onChange={handleFileChange}
        className="hidden"
      />
      {buttons.map((button, index) => {
        const IconComponent = button.icon;
        const buttonContent = (
          <button
            key={button.label}
            onClick={button.onClick}
            className={`w-full p-4 rounded-xl shadow-lg transition-all duration-200 hover:scale-[1.02] hover:shadow-xl group ${
              button.variant === "primary"
                ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white"
                : "bg-white text-gray-700 border border-gray-100"
            }`}
            style={{
              animationDelay: `${index * 100}ms`,
            }}
          >
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${
                button.variant === "primary"
                  ? "bg-white bg-opacity-20"
                  : "bg-gray-50 group-hover:bg-gray-100"
              }`}>
                <IconComponent className="w-5 h-5" />
              </div>
              <div className="text-left flex-1">
                <div className="font-semibold">{button.label}</div>
                <div className={`text-sm ${
                  button.variant === "primary" ? "text-blue-100" : "text-gray-500"
                }`}>
                  {button.description}
                </div>
              </div>
            </div>
          </button>
        );

        if (button.isDrawerTrigger) {
          return (
            <SavedFacesDrawer key={button.label}>
              {buttonContent}
            </SavedFacesDrawer>
          );
        }

        return buttonContent;
      })}
    </div>
  );
};

export default ActionButtons;
