
import { useContacts } from "@/hooks/useContacts";
import { Trash2 } from "lucide-react";

const ContactsView = () => {
  const { contacts, deleteContact } = useContacts();

  if (contacts.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="text-6xl">👤</div>
          <h3 className="text-xl font-semibold text-gray-700">Your Contacts</h3>
          <p className="text-gray-500">Tagged faces will appear here</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-bold text-gray-800">Your Contacts ({contacts.length})</h2>
      <div className="grid gap-4">
        {contacts.map((contact) => (
          <div
            key={contact.id}
            className="bg-white rounded-xl shadow-lg p-4 flex items-center space-x-4"
          >
            <img
              src={contact.imageUrl}
              alt={contact.name}
              className="w-16 h-16 rounded-full object-cover"
            />
            <div className="flex-1">
              <h3 className="font-semibold text-gray-800">{contact.name}</h3>
              <p className="text-sm text-gray-500">
                Tagged on {new Date(contact.timestamp).toLocaleDateString()}
              </p>
            </div>
            <button
              onClick={() => deleteContact(contact.id)}
              className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
            >
              <Trash2 className="w-5 h-5" />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ContactsView;
