import * as faceapi from 'face-api.js';

export interface DetectedFace {
  id: number;
  x: number;
  y: number;
  width: number;
  height: number;
}

let modelsLoaded = false;

const loadModels = async () => {
  if (modelsLoaded) return;
  
  try {
    console.log('Loading face detection models...');
    await Promise.all([
      faceapi.nets.ssdMobilenetv1.loadFromUri('/models'),
      faceapi.nets.faceLandmark68Net.loadFromUri('/models'),
      faceapi.nets.faceRecognitionNet.loadFromUri('/models'),
      faceapi.nets.faceExpressionNet.loadFromUri('/models')
    ]);
    console.log('Face detection models loaded successfully');
    modelsLoaded = true;
  } catch (error) {
    console.error('Error loading face detection models:', error);
    throw error;
  }
};

export const detectFaces = async (
  imageElement: HTMLImageElement,
  minConfidence: number = 0.2 // Lowered confidence threshold
): Promise<DetectedFace[]> => {
  try {
    // Ensure image is loaded
    if (!imageElement.complete) {
      await new Promise((resolve) => {
        imageElement.onload = resolve;
      });
    }

    await loadModels();
    
    // Create a canvas element for face detection
    const canvas = document.createElement('canvas');
    canvas.width = imageElement.width;
    canvas.height = imageElement.height;
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('Could not get canvas context');
    
    // Draw the image on the canvas
    ctx.drawImage(imageElement, 0, 0, canvas.width, canvas.height);
    
    console.log('Detecting faces with confidence threshold:', minConfidence);
    // Detect faces using SSD Mobilenet V1
    const detections = await faceapi.detectAllFaces(
      canvas,
      new faceapi.SsdMobilenetv1Options({ minConfidence })
    );
    
    console.log('Face detections:', detections);
    
    // Convert detections to our format
    const faces = detections.map((detection, index) => ({
      id: index + 1,
      x: (detection.box.x / canvas.width) * 100,
      y: (detection.box.y / canvas.height) * 100,
      width: (detection.box.width / canvas.width) * 100,
      height: (detection.box.height / canvas.height) * 100,
    }));

    console.log('Processed faces:', faces);
    return faces;
  } catch (error) {
    console.error('Error detecting faces:', error);
    return [];
  }
};

export const createImageFromFile = (file: File): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(file);
    
    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve(img);
    };
    
    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image'));
    };
    
    img.src = url;
  });
};
