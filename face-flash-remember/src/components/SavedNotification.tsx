
import { useEffect } from "react";
import { Check, X } from "lucide-react";

interface SavedNotificationProps {
  show: boolean;
  message: string;
  onClose: () => void;
}

const SavedNotification = ({ show, message, onClose }: SavedNotificationProps) => {
  useEffect(() => {
    if (show) {
      const timer = setTimeout(() => {
        onClose();
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [show, onClose]);

  if (!show) return null;

  return (
    <div className="fixed top-20 left-4 right-4 z-50 animate-fade-in">
      <div className="bg-green-500 text-white px-4 py-3 rounded-xl shadow-lg flex items-center space-x-3 max-w-md mx-auto">
        <div className="w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
          <Check className="w-4 h-4" />
        </div>
        <span className="flex-1 font-medium">{message}</span>
        <button
          onClick={onClose}
          className="w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-colors"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export default SavedNotification;
