import { useState } from "react";
import Header from "@/components/Header";
import ImageInteraction from "@/components/ImageInteraction";
import ActionButtons from "@/components/ActionButtons";
import SavedNotification from "@/components/SavedNotification";
import BottomNavigation from "@/components/BottomNavigation";
import RecentFaces from "@/components/RecentFaces";
import ContactsView from "@/components/ContactsView";
import { useContacts } from "@/hooks/useContacts";

const Index = () => {
  const [activeTab, setActiveTab] = useState("photos");
  const [showNotification, setShowNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");
  const { saveContact } = useContacts();
  const [currentImage, setCurrentImage] = useState("https://images.unsplash.com/photo-1529156069898-49953e39b3ac?w=400&h=300&fit=crop");

  const handleSaveContact = (name: string, imageUrl: string) => {
    saveContact(name, imageUrl);
    setNotificationMessage(`Saved ${name} to contacts!`);
    setShowNotification(true);
    setTimeout(() => setShowNotification(false), 3000);
  };

  const handleImageUpload = (file: File) => {
    const imageUrl = URL.createObjectURL(file);
    setCurrentImage(imageUrl);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex flex-col max-w-md mx-auto relative">
      <Header />
      
      <main className="flex-1 px-4 py-6 space-y-6 pb-24">
        {activeTab === "photos" ? (
          <>
            <ImageInteraction onSaveContact={handleSaveContact} currentImage={currentImage} onImageChange={setCurrentImage} />
            <ActionButtons onImageUpload={handleImageUpload} />
            <RecentFaces />
          </>
        ) : (
          <ContactsView />
        )}
      </main>

      <BottomNavigation activeTab={activeTab} onTabChange={setActiveTab} />
      
      <SavedNotification 
        show={showNotification} 
        message={notificationMessage}
        onClose={() => setShowNotification(false)}
      />
    </div>
  );
};

export default Index;
