
import { useState, useEffect } from 'react';

export interface Contact {
  id: string;
  name: string;
  imageUrl: string;
  timestamp: number;
}

export const useContacts = () => {
  const [contacts, setContacts] = useState<Contact[]>([]);

  useEffect(() => {
    const savedContacts = localStorage.getItem('rememberedContacts');
    if (savedContacts) {
      setContacts(JSON.parse(savedContacts));
    }
  }, []);

  const saveContact = (name: string, imageUrl: string) => {
    const newContact: Contact = {
      id: Date.now().toString(),
      name,
      imageUrl,
      timestamp: Date.now(),
    };
    
    const updatedContacts = [newContact, ...contacts];
    setContacts(updatedContacts);
    localStorage.setItem('rememberedContacts', JSON.stringify(updatedContacts));
    return newContact;
  };

  const deleteContact = (id: string) => {
    const updatedContacts = contacts.filter(contact => contact.id !== id);
    setContacts(updatedContacts);
    localStorage.setItem('rememberedContacts', JSON.stringify(updatedContacts));
  };

  const getRecentContacts = (limit: number = 3) => {
    return contacts.slice(0, limit);
  };

  return {
    contacts,
    saveContact,
    deleteContact,
    getRecentContacts,
  };
};
