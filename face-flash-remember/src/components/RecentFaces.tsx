
import { useContacts } from "@/hooks/useContacts";

const RecentFaces = () => {
  const { getRecentContacts } = useContacts();
  const recentFaces = getRecentContacts(3);

  if (recentFaces.length === 0) {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-800 px-1">Recent Faces</h3>
        <div className="text-center py-8">
          <p className="text-gray-500">No tagged faces yet. Start by tagging someone in a photo!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-800 px-1">Recent Faces</h3>
      <div className="flex space-x-4 overflow-x-auto pb-2">
        {recentFaces.map((face) => (
          <div
            key={face.id}
            className="flex-shrink-0 text-center space-y-2 animate-fade-in"
          >
            <div className="relative">
              <img
                src={face.imageUrl}
                alt={face.name}
                className="w-16 h-16 rounded-full object-cover shadow-lg border-2 border-white hover:scale-105 transition-transform duration-200"
              />
              <div className="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                <span className="text-xs text-white">✓</span>
              </div>
            </div>
            <span className="text-sm font-medium text-gray-700 block">{face.name}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecentFaces;
